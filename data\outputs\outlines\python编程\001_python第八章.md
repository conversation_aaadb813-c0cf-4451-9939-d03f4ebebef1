# 函数基础与应用

## 函数概述
### 使用函数可避免重复编写代码，提升编程效率。
### 函数调用让 Python 执行特定任务，简化主程序。
### 函数使程序的编写、阅读、测试和修复更容易。
### 可以通过参数向函数传递信息，实现灵活处理数据。
### 函数可返回处理结果，支持多种复杂数据结构。
### 将函数存储在模块中有助于代码整洁和复用。

## 函数定义规范
### def 关键字用于声明函数，并指定函数名和参数列表。
### 函数体必须缩进，包含实现功能的代码。
### 文档字符串紧随定义，用于描述函数用途。
### 调用函数时需指定函数名和必要参数。
### 函数可多次调用，方便复用代码逻辑。

## 参数传递基础
### 形参是在函数定义中声明的变量，接收调用时传递的数据。
### 实参是在调用函数时传递给形参的实际值。
### 理解形参和实参有助于正确传递和使用数据。
### 名称不规范不会影响功能，但建议遵循标准命名。

## 实参传递方式
### 位置实参需按顺序传递，顺序错误会导致逻辑混乱。
### 关键字实参通过名称传递，顺序无关紧要且更清晰。
### 默认值让部分参数可选，简化函数调用并表明典型用法。
### 可混合使用多种参数传递方式，实现多样化调用。
### 实参数量需与函数定义匹配，错误时 Python 提供详细 traceback。

## 返回值与可选参数
### return 语句用于返回函数处理结果，支持多种类型。
### 可通过默认值让实参变成可选，增加函数灵活性。
### 条件判断可处理有无可选参数的不同情形。
### 函数可以返回字典、列表等复杂结构，便于信息组织和扩展。
### 可选参数和默认值让函数适应更多使用场景。

## 函数与控制结构结合
### 函数能在 while 循环中多次调用，实现重复任务处理。
### 可为循环提供退出条件，提升程序友好性。
### 结合输入和输出，函数能增强用户交互体验。
### 在循环中调用函数可简化代码结构和逻辑。

## 列表参数传递
### 传递列表使函数可访问和处理其中所有元素。
### 函数可对列表进行修改，操作结果影响传入列表。
### 用函数组织代码提升逻辑清晰度和可维护性。
### 各函数应专注一项任务，利于分工和复用。
### 可通过传递列表副本保护原始数据，防止无意修改。

## 任意数量参数
### 星号 * 可收集任意数量的位置实参为元组。
### 结合位置实参和 *args 可同时接受确定和不确定数量参数。
### 双星号 ** 可收集任意数量的关键字实参与字典。
### 可同时使用位置实参、*args 和 **kwargs，满足多样调用需求。
### 函数调用支持多种实参类型，便于处理不同数据和业务场景。

## 模块化管理函数
### 模块是扩展名为 .py 的文件，存放相关函数代码。
### import 语句可导入整个模块或特定函数。
### as 关键字可为模块或函数指定别名，简化调用和避免冲突。
### 星号 * 可导入模块中所有函数，但建议只导入所需部分。
### 模块化让代码易于复用、共享和维护，提升开发效率。

## 编码规范与风格
### 函数和模块名应描述性强，只用小写和下划线。
### 每个函数应包含文档字符串，简述功能便于他人理解。
### 默认值等号两侧无空格，保持一致性。
### 代码行不超过 79 字符，多形参函数可分行缩进。
### 相邻函数间用两空行分隔，所有 import 语句放文件开头。

## 函数优势总结
### 复用函数代码减少重复，提高维护和修改效率。
### 良好函数名和调用结构让程序更易理解。
### 函数分工明确有助于测试和调试，提高可靠性。
### 编写和使用函数是高效编程的重要基础。
### 下一章将学习类，进一步提升代码封装与组织能力。